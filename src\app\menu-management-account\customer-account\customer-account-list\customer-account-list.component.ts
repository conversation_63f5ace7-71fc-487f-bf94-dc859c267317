import { Employee<PERSON><PERSON><PERSON><PERSON>, Particular, Retailer, User } from 'src/app/shared/models/user.models';
import { Component, Inject, LOCALE_ID, OnInit, ViewEncapsulation } from '@angular/core';
import { EmployeeType, UserCategory } from 'src/app/shared/enums/user-category.enum';
import { CommonService } from '../../../shared/services/common.service';
import { ConfirmationService, MessageService } from 'primeng/api';
import { t } from 'src/app/shared/functions/global.function';
import { APP_BASE_HREF, Location } from '@angular/common';
import { HttpErrorResponse } from '@angular/common/http';
import { UserService } from '../../services/user.service';


@Component({
  selector: 'mcw-customer-account-list',
  templateUrl: './customer-account-list.component.html',
  styles: [],
  providers: [ConfirmationService, MessageService],
  encapsulation: ViewEncapsulation.None,
})
export class CustomerAccountListComponent implements OnInit {
  userType: string = this.commonService.setStatusLabel(UserCategory.Particular);
  drhTitle: string;
  title: string = '';
  showSideBar: boolean;
  modalReset: boolean;
  modalCodeAfriland: boolean;
  isLoading: boolean;
  showDialogDetail: boolean;
  showDialogExport: boolean;
  dataUser: any;
  isLoadingInModal: boolean;
  confirmPassword = '';
  filteredUsers: any;
  usersEmails: any;
  usersTels: any;
  isDrhOrRh: boolean;

  users: Particular[] | Retailer[] | EmployeeCimencam[] = [];
  user: User = {
    password: ''
  };

  employeeType = EmployeeType;

  statusAccount = [
    { name: 'Actif', code: true, },
    { name: 'Inactif', code: false, },
  ];

  statusAccountEn = [
    { name: 'Active', code: true, },
    { name: 'Inactive', code: false, },
  ];

  offset = 0;
  limit = 50;
  total = 0;
  language = this.baseHref?.replace(/\//g, '');

  TypeEmployee: { name: string; value: EmployeeType }[] = [
    { name: 'DRH', value: EmployeeType.DRH },
    { name: 'Coordo RH', value: EmployeeType.CORDO_RH },
    { name: 'Employé(e) simple', value: EmployeeType.NORMAL },
  ];
  isValidated: boolean;

  constructor(
    public commonService: CommonService,
    public userService: UserService,
    private confirmationService: ConfirmationService,
    private messageService: MessageService,
    private location: Location,
    @Inject(APP_BASE_HREF) private baseHref: string
  ) { }

  async ngOnInit(): Promise<void> {
    this.statusAccount = (this.language === 'en') ? this.statusAccountEn : this.statusAccount;
    this.drhTitle = `${await t('EMPLOYEE')}`;
    this.isDrhOrRh = [EmployeeType.CORDO_RH, EmployeeType.DRH].includes(this.commonService?.user?.employeeType);
    await this.getTitle(this.userType);
    this.userType = this.commonService.setStatusLabel(UserCategory.Particular);
    !this.isDrhOrRh ? this.handleChangeTabUser() : this.handleChangeTabUserForDrhORCoordoRh()
    await this.getElementsForFilters();

  }

  async paginate(event: any): Promise<void> {
    this.limit = event.rows;
    this.offset = event.first;
    !this.isDrhOrRh ? this.handleChangeTabUser() : this.handleChangeTabUserForDrhORCoordoRh()
  }

  async getUsers(): Promise<boolean> {
    this.isLoading = true;
    this.showSideBar = false;
    const query = {
      ...this.userService.filterForm,
      category: UserCategory[this.userType],
      offset: this.offset,
      limit: this.limit,
      isValidated: this.isValidated
    };
    const result = await this.userService.getUsers(query);
    if (result instanceof HttpErrorResponse) {
      this.messageService.add({
        severity: 'error',
        summary: `${await t('GETDATA_ERROR')}`,
        detail: result.error.message
      });
      return this.isLoading = false;
    }
    this.total = result.count
    this.users = result?.data;
    return this.isLoading = false;
  }

  async getElementsForFilters() {
    const keyForFilters = ['email', 'tel'];
    const usersInfos = await this.commonService.getElementForFilterByKeys('users', { keyForFilters })
    this.usersEmails = usersInfos?.dataemail;
    this.usersTels = usersInfos?.datatel;
  }

  searchUsers(event: any) {
    this.filteredUsers = this.usersEmails.filter((item) =>
      item.toLowerCase().includes(event.query.toLowerCase())
    ).sort((a, b) => {
      return a.toString().toLowerCase().indexOf(event.query?.toLowerCase()) - b.toString().toLowerCase().indexOf(event.query?.toLowerCase())
    });
  }

  async getUsersEmployeeToValidate(): Promise<boolean> {
    this.isLoading = true;
    delete this.userService.filterForm.enable;
    const query = {
      ...this.userService.filterForm,
      category: UserCategory[this.userType],
      offset: this.offset,
      limit: this.limit,
      isValidated: this.isValidated,
      enable: false,
    };
    const result = await this.userService.getUsers(query);
    if (result instanceof HttpErrorResponse) {
      this.messageService.add({
        severity: 'error',
        summary: `${await t('GETDATA_ERROR')}`,
        detail: result.error.message
      });
      return this.isLoading = false;
    }
    this.total = result.count
    this.users = result?.data;
    return this.isLoading = false;
  }

  async handleChangeTabUser(): Promise<void | boolean> {
    this.isLoading = true;

    if (this.userService.indexTabClient === 0) {
      this.userType = this.commonService.setStatusLabel(UserCategory.Particular);
      delete this.userService.filterForm.employeeType;
      delete this.isValidated;
    }
    if (this.userService.indexTabClient === 1) {
      this.userType = this.commonService.setStatusLabel(UserCategory.RETAILER);
      delete this.userService.filterForm.employeeType;
      delete this.isValidated;
    }
    if (this.userService.indexTabClient === 2) {
      this.userType = this.commonService.setStatusLabel(UserCategory.EmployeeEntity);
      this.isValidated = true;
    }

    if (this.userService.indexTabClient === 3) {
      this.userType = this.commonService.setStatusLabel(UserCategory.EmployeeEntity);
      this.isValidated = false;
      this.title = `${await t('EMPLOYEE')}`;
      console.log(this.title);

      return await this.getUsersEmployeeToValidate();
    }
    await this.getTitle(this.userType);
    await this.getUsers();
  }

  async handleChangeTabUserForDrhORCoordoRh(): Promise<void | boolean> {
    this.isLoading = true;
    if (this.userService.indexTabClient === 0) {
      this.userType = this.commonService.setStatusLabel(UserCategory.EmployeeEntity);
      this.isValidated = true;
    }

    if (this.userService.indexTabClient === 1) {
      this.userType = this.commonService.setStatusLabel(UserCategory.EmployeeEntity);
      this.isValidated = false;
      this.title = `${await t('EMPLOYEE')}`;
      return await this.getUsersEmployeeToValidate();
    }
    await this.getTitle(this.userType);
    await this.getUsers();
  }

  async deleteUser(user: User) {
    this.confirmationService.confirm({
      message: `${await t('CONFIRM-DISABLE')} ${user.enable ? await t('disable') : await t('enable')} ${await t('CONFIRM-DISABLEUSER')} ?`,
      header: `${user.enable ? await t('Disable') : await t('Enable')} ${await t('Of')} ${user?.firstName || user?.lastName}`,
      icon: 'pi pi-info-circle',
      accept: async () => {
        this.isLoading = true;
        this.isLoadingInModal = true;
        const res = await this.userService.deleteUser(user);
        await this.getUsers();
        this.messageService.add({
          severity: (res?.status === 200) ? 'success' : 'error',
          summary: (res?.status === 200) ? `${user.enable ? await t('Disable') : await t('Enable')} ${'Done'}` : res.data,
          detail: '' + res?.message,
        });
        this.isLoadingInModal = false;
      },

    });
  }

  async validateAccount(user: EmployeeCimencam): Promise<void> {
    this.confirmationService.confirm({
      message: `${await t('VALIDATED_ACCOUNT')} ${user?.firstName}`,
      header: `${await t('CONFIRM_VALIDATION')}`,
      icon: 'pi pi-info-circle',
      accept: async () => {
        this.isLoading = true;
        this.isLoadingInModal = true;
        const res = await this.userService.ValidateAccountEmployee(user);
        this.userService.filterForm.enable = false;
        await this.getUsersEmployeeToValidate();
        this.messageService.add({
          severity: (res?.status === 200) ? 'success' : 'error',
          summary: (res?.status === 200) ? `${user.enable ? await t('Disable') : await t('Enable')} ${'Done'}` : res.data,
          detail: '' + res?.message,
        });
        this.isLoadingInModal = false;
      },
    });
  }

  back(): void {
    this.location.back();
  }

  showModalcodeAfriland(user: User) {
    this.modalCodeAfriland = true;
    this.user = user;
  }
  async sendOtp(user: User) {
    this.isLoadingInModal = true;
    const email = user.email;
    const res = await this.userService.getAfrilandCode(email);
    if (res.status === 201) {
      this.messageService.add({
        severity: 'success',
        summary: `${await t('GENERATE_CODE')}`,
      });
      this.modalCodeAfriland = false;
    } else {
      this.messageService.add({
        severity: 'error',
        summary: '' + res.data,
        detail: '' + res.message,
      });
    }
    return this.isLoadingInModal = false;
  }
  showModalReset(user: User) {
    this.modalReset = true;
    this.user = user;
  }

  async resetpassword(): Promise<boolean> {
    this.isLoadingInModal = true;
    if (this.user.password !== this.confirmPassword) {
      this.messageService.add({ severity: 'error', summary: `${await t('DATA_ERROR')}`, detail: `${await t('DIFF_PASSWORD')}` });
      return this.isLoadingInModal = false;
    }
    const res = await this.userService.changePassword(this.user);
    if (res.status === 200) {
      this.messageService.add({
        severity: 'success',
        summary: `${await t('DATA_RESET')}`,
        detail: '' + res.message,
      });
      this.modalReset = false;
    } else {
      this.messageService.add({
        severity: 'error',
        summary: '' + res.data,
        detail: '' + res.message,
      });
    }
    return this.isLoadingInModal = false;
  }

  async reset(): Promise<void> {
    this.offset = 0;

    this.userService.filterForm = {
      email: '',
      tel: '',
      employeeType: '',
      enable: true,
    };
    await this.getUsers();
  }

  async getTitle(user: string) {
    if (user === 'Particular') { this.title = `${await t('PARTICULAR')}` }
    if (user === 'RETAILER') { this.title = `${await t('RESELLER')}` }
    if (user === 'EmployeeCimencam') { this.title = `${await t('EMPLOYEE')}` }
  }

  getColor(category: number): string {
    if (category === EmployeeType.CORDO_RH) { return 'bg-warning-100' }
    if (category === EmployeeType.DRH) { return 'bg-primary-100' }
    if (category === EmployeeType.NORMAL) { return 'bg-info-100' }
    return 'bg-info-100';
  }


  async exportToExcel() {
    this.isLoading = true;
    await this.getUsers();
    this.dataUser = this.users.map(elt => {
      const data = {};
      data['NOM'] = elt?.lastName || 'N/A';
      data['EMAIL'] = elt?.email || 'N/A';
      data['CNI'] = elt?.cni;
      data['REGION'] = elt?.address?.region || 'N/A';
      data['VILLE'] = elt?.address?.city || 'N/A';
      data['TELEPHONE'] = elt?.tel || 'N/A';
      data['TYPE UTILISATEUR'] = EmployeeType[elt?.employeeType] || 'N/A';
      return data;
    });
    this.commonService.exportRetriveExcelFile(this.dataUser, 'Liste des utiilisateurs');
    this.isLoading = false;
  }
}
