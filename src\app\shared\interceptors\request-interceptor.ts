import {
  HttpInterceptor,
  HttpRequest,
  <PERSON>ttp<PERSON><PERSON><PERSON>,
  HttpEvent,
} from '@angular/common/http';
import { Inject, Injectable } from '@angular/core';
import { Observable, from } from 'rxjs';
import { CommonService } from 'src/app/shared/services/common.service';
import { APP_BASE_HREF } from '@angular/common';

@Injectable()
export class RequestInterceptor implements HttpInterceptor {
  constructor(
    private commonSrv: CommonService,
    @Inject(APP_BASE_HREF) private baseHref: string
  ) { }

  intercept(req: HttpRequest<any>, next: HttpHandler): Observable<HttpEvent<any>> {
    let headers = req.headers;

    // Ajouter la langue à partir de baseHref (ex: 'fr', 'en')
    if (this.baseHref) {
      headers = headers.set('Accept-Language', this.baseHref.replace(/\//g, ''));
    }

    // Ajouter le token si présent et pas déjà dans la requête
    const token = this.commonSrv?.user?.accessToken;
    if (token && !req.headers.has('Authorization')) {
      headers = headers.set('Authorization', `Bearer ${token}`);
      console.log('Token added to headers:', token);
    }

    return next.handle(req.clone({ headers }));
  }

}
