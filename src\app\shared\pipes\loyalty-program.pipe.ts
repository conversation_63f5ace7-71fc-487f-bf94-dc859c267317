import { Pipe, PipeTransform } from '@angular/core';
import { FidelityStatus } from '../models/user.models';

@Pipe({
  name: 'loyaltyProgramLevelLabel'
})
export class LoyaltyProgramPipe implements PipeTransform {

  transform(value: FidelityStatus, ...args: unknown[]): unknown {
    let label: string
    switch (value) {
      case FidelityStatus.AMIGO:
        label = 'AMIGO';
        break;
      case FidelityStatus.COLOMBE:
        label = 'COLOMBE';
        break;
      case FidelityStatus.PELICAN:
        label = 'PELICAN';
        break;

      default:
        label = '0';
        break;
    }
    return label;
  }

}

@Pipe({
  name: 'loyaltyProgramLevelColor'
})
export class LoyaltyProgramColorPipe implements PipeTransform {

  transform(value: FidelityStatus, ...args: unknown[]): unknown {
    let label: string
    switch (value) {
      case FidelityStatus.AMIGO:
        label = 'AMIGO';
        break;
      case FidelityStatus.COLOMBE:
        label = 'COLOMBE';
        break;
      case FidelityStatus.PELICAN:
        label = 'PELICAN';
        break;

      default:
        label = 'N/A';
        break;
    }
    return label;
  }

}
