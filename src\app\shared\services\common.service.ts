import { CompanyCategory } from '../enums/Company-category.enum';
import { UserCategory } from './../enums/user-category.enum';
import { QueryResult } from './../models/query-result';
import * as XLSX from 'xlsx';
import * as FileSaver from 'file-saver';
import { Inject, Injectable } from '@angular/core';
import { HttpClient, HttpErrorResponse, HttpParams } from '@angular/common/http';
import { cities, commercialRegions, regions } from '../mocks/mocks';
import { AuthUser, User } from '../models/user.models';
import { APP_BASE_HREF } from '@angular/common';
import { lastValueFrom } from 'rxjs';
import { Address, Point, QueryFilter } from '../types';
import { BaseUrlService } from './base-url.service';
import { environment } from 'src/environments/environment';
import { MessageService } from 'primeng/api';
import { UserService } from 'src/app/menu-management-account/services/user.service';

@Injectable({
  providedIn: 'root',
})
export class CommonService {
  url: string;
  isLoading: boolean;
  user: AuthUser;
  showAuthModal: boolean;
  showAuthOtpModal: boolean;
  showAuthOtpCodeModal: boolean;
  userCategory = UserCategory;
  EXCEL_TYPE = 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet;charset=UTF-8';
  EXCEL_EXTENSION = '.xlsx';

  companyCategory = [
    { name: 'Baker', code: CompanyCategory.Baker },
    { name: 'Grossiste', code: CompanyCategory.Baker },
    { name: 'GMS', code: CompanyCategory.GMS },
    // { name: 'Export', code: CompanyCategory.Distributor5 },
    // { name: 'Enseigne', code: CompanyCategory.Distributor4 },
  ];

  constructor(
    private http: HttpClient,
    @Inject(APP_BASE_HREF) private baseHref: string,
    private baseUrlService: BaseUrlService,
    private commonSrv: CommonService,
    private userService: UserService,
    private messageService: MessageService,

  ) {
    this.url =
      this.baseUrlService.getOrigin() + environment.basePath;
  }

  getRegions(): string[] {
    return regions;
  }

  get commercialRegions() {
    return commercialRegions;
  }

  getCities(region: string): string[] {
    return cities[region];
  }

  //TODO: change it to a pipe
  setStatusLabel(label: string | number): string {
    const statusValue = typeof label === 'number' ? UserCategory[label] : label;
    // return statusValue.replace('_', ' ');
    return statusValue;
  }
  //TODO: Change this method in pipe
  setStatusCompanyLabel(label: string | number): string {
    const statusValue =
      typeof label === 'number'
        ? CompanyCategory[label] || UserCategory[label]
        : label;
    // return statusValue.replace('_', ' ');
    return statusValue;
  }

  getUserTypes(): string[] {
    return Object.keys(UserCategory).filter((value) =>
      isNaN(Number(value))
    ) as string[];
  }

  async getError(title: string, error: any): Promise<QueryResult> {        
    if (error?.error?.errors) {
      for (const err of error?.error?.errors) {
        error.error.message = err + '; ';
      }
    }
    return {
      status: error?.error?.statusCode,
      message: error?.error?.message || error?.message,
      data: title,
    };
  }

  //TODO: Optimize this code and share it
  verifyAllFieldsForm(object: Object): string | boolean {
    for (const key in object) {
      if ([null, 'null', undefined, 'undefined', NaN, 'NaN', '', "", 0].includes(object[key])) {
        console.log('first level:', key, '=', object[key]);
        return `Veuillez renseigner tous les champs: ${key}`;
      }

      if (key === 'tel' && !/^6[0-9]{8}$/.test(object[key])) {
        console.log(key + ': ' + object[key]);
        return 'Renseignez le bon format du numéro de téléphone, ex: 6xXxXxXxX';
      }

      if (key === 'price' && (object[key] === 0 || object[key] < 0)) {
        return 'Le montant doit être supérieur à zéro';
      }

      if (!/^\w+([\.-]?\w+)*@\w+([\.-]?\w+)*(\.\w{2,3})+$/.test(object[key]) && key === 'email') {
        return 'Veuillez entrer un email valide';
      }

      if (typeof object[key] === 'object') {
        const newObject = object[key];
        for (const keyInObject in newObject) {
          if (!newObject[keyInObject]) {
            console.log('key in Object', keyInObject);
            return `Veuillez renseigner tous les champs: ${keyInObject}`;
          }
        }
      }
    }

    return false;
  }


  verifyFieldsForm(object: Object, optionalFields: string[] = []): string | boolean {
    for (const key in object) {
      // Si le champ est optionnel, on l'ignore
      if (optionalFields.includes(key)) {
        continue;
      }

      // Vérification des valeurs invalides
      if (object[key] === null || object[key] === undefined || (typeof object[key] === 'string' && object[key].trim() === '')) {
        return `Veuillez renseigner tous les champs: ${key}`;
      }

      // Vérification du format du numéro de téléphone
      if (key === 'tel' && !/^6[0-9]{8}$/.test(object[key])) {
        console.log(key + ': ' + object[key]);
        return 'Renseignez le bon format du numéro de téléphone, ex: 6xXxXxXxX';
      }

      // Vérification du prix
      if (key === 'price' && (object[key] === 0 || object[key] < 0)) {
        return 'Le montant doit être supérieur à zéro';
      }

      // Vérification de l'email
      if (key === 'email' && !/^\w+([\.-]?\w+)*@\w+([\.-]?\w+)*(\.\w{2,3})+$/.test(object[key])) {
        return 'Veuillez entrer un email valide';
      }

      // Vérification des objets imbriqués
      if (typeof object[key] === 'object' && object[key] !== null) {
        const newObject = object[key];
        for (const keyInObject in newObject) {
          // Vérifier si le champ imbriqué est optionnel
          if (optionalFields.includes(keyInObject)) {
            continue;
          }

          if (!newObject[keyInObject]) {
            console.log('key in Object', keyInObject);
            return `Veuillez renseigner tous les champs: ${keyInObject}`;
          }
        }
      }
    }

    return false;
  }


  clean(input: any[] | { [key: string]: any }) {
    return Array.isArray(input) ? this.cleanArray(input) : this.cleanObject(input);
  }

  private cleanArray(array: any[]) {
    return array
      .map(value => value && typeof value === 'object' ? this.clean(value) : value)
      .filter(value => value !== null && value !== undefined);
  }

  private cleanObject(obj: { [key: string]: any }) {
    return Object.entries(obj)
      .map(([key, value]) => [key, value && typeof value === 'object' ? this.clean(value) : value])
      .reduce((accumulator, [key, value]) => {
        if (value !== null && value !== undefined) {
          accumulator[key] = value;
        }
        return accumulator;
      }, {});
  }

  initPoint(): Point {
    return {
      validated: 0,
      unvalidated: 0,
      archived: 0,
    };
  }

  generateHexColor() {
    const randomColor = Math.floor(Math.random() * 16777215).toString(16);
    return '#' + '0'.repeat(6 - randomColor.length) + randomColor;
  }

  initAddress(): Address {
    return {
      region: '',
      city: '',
      district: '',
    };
  }

  generateRandomString(length: number) {
    let result = '';
    const characters = 'ABCDEFGHIJKLMNOPQRSTUVWXYZ_-$abcdefghijklmnopqrstuvwxyz*0123456789&@';
    const charactersLength = characters.length;
    for (let i = 0; i < length; i++) {
      result += characters.charAt(Math.floor(Math.random() * charactersLength));
    }
    return result;
  }

  public async getJSON(): Promise<any> {
    return await lastValueFrom(
      this.http.get(
        `./assets/Other-translate/${this.baseHref?.replace(
          /\//g,
          ''
        )}/local.json`
      )
    );
  }

  async getElementForFilterByKeys(module: string, param: any): Promise<any> {

    try {
      let params = new HttpParams();
      const { keyForFilters, status } = param;
      if (keyForFilters) params = params.append('keyForFilters', keyForFilters);
      if (status) params = params.append('status', status);
      return await lastValueFrom(
        this.http.get<any>(`${this.url}/${module}/filters-elements`, {
          params,
        })
      );

    } catch (error) {
      return this.commonSrv.getError('Une erreur s\'est produite', error);
    }
  }

  exportRetriveExcelFile(json: any[], excelFileName: string): void {
    const worksheet: XLSX.WorkSheet = XLSX.utils.json_to_sheet(json);
    worksheet['!cols'] = [{ wch: 30 }, { wch: 30 }, { wch: 15 }, { wch: 30 }, { wch: 15 }, { wch: 25 }, { wch: 15 }, { wch: 15 }];

    const workbook: XLSX.WorkBook = {
      Sheets: { data: worksheet },
      SheetNames: ['data']
    };

    const excelBuffer: any = XLSX.write(workbook, {
      bookType: 'xlsx',
      type: 'array'
    });

    this.saveAsExcelFile(excelBuffer, excelFileName);
  }

  saveAsExcelFile(buffer: any, fileName: string): void {
    const data: Blob = new Blob([buffer], {
      type: this.EXCEL_TYPE
    });
    FileSaver.saveAs(
      data, fileName + '_export_' + new Date().getDate() + ':' + new Date().getHours() + ':' + new Date().getMinutes()
      + ':' + new Date().getSeconds() + this.EXCEL_EXTENSION
    );
  }

  converTValueOfKeyOfObjectInArrayToString(array: Object[]): any[] {
    console.log('array:', array);

    return array.map(obj => obj = {
      availableBalance: (+obj['Plafond de crédit'] || 0) - (+obj['Solde Comptable'] || 0) - (+obj['Commandes ouvertes'] || 0) - (+obj['Autres notes de débit'] || 0),
      clientCode: obj['Code Client'],
      creditLimit: obj['Plafond de crédit'],
      invoiceInProgress: obj['Commandes Ouvertes'],
      invoicedDelayed: obj['Factures Echues'],
      openOrderAmount: obj['Total Encours'],
      // openOrderAmount: obj['Solde Comptable'],
      othersAmount: obj['Autres notes de débit'],
      date: `${obj["Date d'extraction"]}`,
      hours: obj['Heures'],
      ...obj,
    }
      //   {
      //   return Object.entries(obj).reduce((acc, [key, value]) => {
      //     acc[key] = String(value);
      //     return acc;
      //   }, {});
      // }

    );
  }

  // removeDuplicateObjectCodeInArray(array: any[], keyCodeInObject: string) {
  //   const indexDuplicate = array.reduce((indices, valeur, index) => {
  //     if (valeur === valeur[`${keyCodeInObject}`]) {
  //       indices.push(index);
  //     }
  //     return indices;
  //   }, []);

  //   if (indexDuplicate?.length) {
  //     console.log(indexDuplicate);
  //     return array = array.filter((balance, i) => indexDuplicate.includes(i))
  //   }
  //   return array;
  // }

  removeDuplicateObjectCodeInArray(array: any[], keyCodeInObject: string): { array: any[], indexDuplicates: number[] } {
    const sameValue = [];
    const indexDuplicates = [];

    array = array.filter((objet, index) => {
      const valeurKey = objet[keyCodeInObject];
      if (!sameValue.includes(valeurKey)) {
        sameValue.push(valeurKey);
        return true;
      }

      indexDuplicates.push(index);
      return false;
    });

    return { array, indexDuplicates }
  }
  // projection: { email: 1, firstName: 1, lastName: 1, tel: 1, _id: 0 }

  async fetchCompanyUsers(query:any): Promise<{ data: User[] }> {

    return await this.userService.getUsers(query) as unknown as { data: User[] };
  }

}
