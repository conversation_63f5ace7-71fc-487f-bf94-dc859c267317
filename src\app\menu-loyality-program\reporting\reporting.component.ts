import { APP_BASE_HREF } from '@angular/common';
import { Component, Inject, OnInit } from '@angular/core';
import { MessageService } from 'primeng/api';
import { Product } from 'src/app/shared/models/product.model';
import { t } from 'src/app/shared/functions/global.function';
import { ReportingService } from './service/reporting.service';
import { HttpErrorResponse } from '@angular/common/http';
import { OrderResellerStatus } from 'src/app/shared/models/orderRetail';
import { CommonService } from 'src/app/shared/services/common.service';
import { ConfirmationService } from 'primeng/api';
import { OrdersreportingService } from 'src/app/menu-reporting/orders-reporting/services/ordersreporting.service';
import { User } from 'src/app/shared/models/user.models';

@Component({
  selector: 'mcw-reporting',
  templateUrl: './reporting.component.html',
  styles: [
  ],
  providers: [
    MessageService,
    ReportingService,
    ConfirmationService
  ]
})
export class ReportingComponent implements OnInit {

  dataTotalPoints: unknown[];
  dataDistributorVolume: unknown[];
  dataStateOrder: any;
  dataStateProduct: any;
  sideBarDisplay: boolean;
  isLoading: boolean;
  products: Product[];
  productsForFilters: any;
  showDialogArchive: boolean;

  dataEanPoint: any;
  dataProductEvolutions: any;
  pointData: any
  produtData: any;
  charType: string = 'dataYear';
  chartTypeProduct: string = 'dataYear'
  session: string;
  filterForm = {
    startDate: 0,
    endDate: 0,
    product: '',
    region: '',
  };

  region = [
    { name: 'LSO', code: 'LSO' },
    { name: 'CS', code: 'CS' },
    { name: 'GNO 1', code: 'GNO 1' },
    { name: 'ONO', code: 'ONO' },
    { name: 'GNO 2', code: 'GNO 2' },
  ];

  orderStatus = [
    { name: 'Créer', code: OrderResellerStatus.CREATED },
    { name: 'Prevalidé', code: OrderResellerStatus.PREVAIDATED },
    { name: 'Réjéter', code: OrderResellerStatus.REJECTED },
    { name: 'Valider', code: OrderResellerStatus.VALIDATED },
  ];
  months = ['Jan', 'Feb', 'Mar', 'Ap', 'May', 'Jun', 'July', 'aug', 'sep', 'oct', 'nov', 'dec']


  chartTypes: unknown[] = [{ name: 'Mois', key: 'dataMonth' }, { name: 'Année', key: 'dataYear' }];
  language = this.baseHref?.replace(/\//g, '');
  productData: { labels: string[]; datasets: any[]; };
  isLoadingInModal: any;
  user: User;
  constructor(private messageService: MessageService,
    private reportingService: ReportingService,
    private confirmationService: ConfirmationService,
    private orderSrv: OrdersreportingService,
    private commonSrv: CommonService,
    @Inject(APP_BASE_HREF) private baseHref: string
  ) { }

  async ngOnInit(): Promise<void> {
    await this.refresh();
    await this.getElementsForFilters();
    this.user = this.commonSrv?.user;
  }

  async reset(): Promise<void> {
    this.filterForm = {
      startDate: null,
      endDate: null,
      product: null,
      region: null,

    };
  }

  async refresh(): Promise<void> {
    try {
      if ((this.filterForm.startDate || this.filterForm.endDate)
        && (this.filterForm.startDate > this.filterForm.endDate)) {
        this.messageService.add({ severity: 'error', summary: '', detail: await t('ERROR_INTERVAL_DATE') });

        return;
      }
      
      const query = Object.fromEntries(
        Object.entries(this.filterForm).filter(([key, value]) => value !== null && value !== undefined)
      );
      
      await this.getTotalPoint();
      await this.getDistributorVolume();
      await this.geTotalQuantityOrder();
      await this.getTotalProduct();
      // await this.generateDataForEvolutionSalesProducts()
      await this.getDataEvolutionsPoints();
      await this.generateDataEvolutionsProducts();

    } catch (error) {
      return error;
    }
  }

  async getTotalPoint() {
    this.isLoading = true;


    const result = await this.reportingService.getTotalPoints({ ...this.filterForm });

    if (result instanceof HttpErrorResponse) {

      this.messageService.add({
        severity: 'error',
        summary: `${await t('CONNECTION')}`,
        detail: result.error.message,
      });
      return (this.isLoading = false);
    }
    this.dataTotalPoints = result;
    return (this.isLoading = false);
  }

  async geTotalQuantityOrder() {
    this.isLoading = true;
    const result = await this.reportingService.geTotalQuantityOrder({ ...this.filterForm });
    if (result instanceof HttpErrorResponse) {
      this.messageService.add({
        severity: 'error',
        summary: `${await t('CONNECTION')}`,
        detail: result.error.message,
      });
      return (this.isLoading = false);
    }
    this.dataStateOrder = result;


    return (this.isLoading = false);
  }

  async getTotalProduct() {
    this.isLoading = true;
    const result = await this.reportingService.getTotalProduct({ ...this.filterForm });;
    this.products = result;
    if (result instanceof HttpErrorResponse) {
      this.messageService.add({
        severity: 'error',
        summary: `${await t('CONNECTION')}`,
        detail: result.error.message,
      });
      return (this.isLoading = false);
    }
    this.dataStateProduct = result;


    return (this.isLoading = false);
  }

  async getElementsForFilters() {
    const keyForFilters = ['label'];
    const orderNumbers = await this.commonSrv.getElementForFilterByKeys('products', { keyForFilters })
    this.productsForFilters = orderNumbers?.datalabel;
  }

  async getDistributorVolume(): Promise<boolean> {
    this.isLoading = true;
    const result = await this.reportingService.getDistributorVolume({...this.filterForm});
    if (result instanceof HttpErrorResponse) {
      this.messageService.add({
        severity: 'error',
        summary: `${await t('CONNECTION')}`,
        detail: result.error.message,
      });
      return (this.isLoading = false);
    }
    this.dataDistributorVolume = result;
    return (this.isLoading = false);
  }

  async generateDataForEvolutionSalesProducts(): Promise<void> {
    this.isLoading = true;

    this.productData = {
      labels: this.dataStateProduct.map((data: { label: string; }) => data.label),
      datasets: [
        {
          label: 'Variaton des produits',
          data: [],
          backgroundColor: [],
          borderColor: [],
          borderWidth: 1
        }
      ]
    };

    this.dataStateProduct.forEach((data, i) => {
      const color = this.generateHexColor();
      this.productData.datasets[0].data.push(data.totalTone);
      this.productData.datasets[0].backgroundColor.push(color);
      this.productData.datasets[0].borderColor.push(color);
    });
    this.isLoading = false;
  }

  async getDataEvolutionsPoints() {
    this.isLoading = true;
    const data = this.dataEanPoint = await this.reportingService.getTotalEarnPoint({ ...this.filterForm });
    this.generateDataEvolutionsPoint();
    this.isLoading = false;
  }

  generateDataEvolutionsPoint(event?: any): void {
    this.pointData = {
      labels: this.dataEanPoint[`${this.charType}`].labels,
      datasets: [
        {
          label: 'Nombre de validation des points',
          data: this.dataEanPoint[`${this.charType}`].data,
          tension: .4,
        }
      ]
    };

  }

  async generateDataEvolutionsProducts(event?: any): Promise<void> {
    this.isLoading = true;
    this.dataProductEvolutions = await this.reportingService.getSalesEvolutionByProductSuppliers({ ...this.filterForm });
    this.getDataEvolutionProduct();
    
    this.isLoading = false;
  }

  getDataEvolutionProduct() {
    this.productData = {
      labels: this.months,
      datasets: []
    };

    const colors = ['#0071a6', '#b2b29e', '#fdfda5', 'var(--clr-primary0d7d3d-400)', '#ee2f3d', '#036178'];
    let i = 0;

    for (const key in this.dataProductEvolutions) {
      const i = Object.keys( this.dataProductEvolutions).findIndex(elt => elt === key);
      const color = colors[i] ? colors[i] : `rgb(${(255 % (i + 5))},${(255 / (i + 1))} , ${(255 - (i + 10))})`;
      colors[key] = color;

      this.productData.datasets.push({
        label: key,
        data: this.dataProductEvolutions[key],
        fill: false,
        tension: .4,
        backgroundColor: color,
        borderColor: color,
      });
    }
  }


  async archivedPoint() {
    this.isLoadingInModal = true;
    try {
      await this.reportingService.archivePoints({ session: this.session });
      this.showDialogArchive = false;
      this.session = null;
      this.messageService.add({
        severity: 'success',
        summary: ` SUCCESS`,
        detail: 'Retails  point archived',
      });
    } catch (error) {
      this.messageService.add({
        severity: 'error',
        summary: `Erreur`,
        detail: error.message,
      });

      return error;
    } finally {
      this.isLoadingInModal = false
    }
  }


  generateHexColor() {
    const randomColor = Math.floor(Math.random() * 16777215).toString(16);
    return '#' + '0'.repeat(6 - randomColor.length) + randomColor;
  }
}
