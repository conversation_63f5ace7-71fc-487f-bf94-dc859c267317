// Script de test pour diagnostiquer le problème d'autorisation
// À utiliser temporairement dans le composant de reporting

export function debugAuthorizationIssue(commonService: any, reportingService: any) {
  console.log('=== DEBUG AUTORISATION ===');
  
  // Vérifier l'utilisateur connecté
  const user = commonService?.user;
  console.log('Utilisateur connecté:', {
    id: user?._id,
    email: user?.email,
    roles: user?.roles,
    hasBackofficeRole: user?.roles?.includes('backoffice'),
    authorizationsCount: user?.authorizations?.length,
    firstFewAuthorizations: user?.authorizations?.slice(0, 10)
  });
  
  // Vérifier le token
  console.log('Token d\'accès:', {
    exists: !!user?.accessToken,
    length: user?.accessToken?.length,
    startsWithBearer: user?.accessToken?.startsWith('Bearer'),
    preview: user?.accessToken?.substring(0, 20) + '...'
  });
  
  // Vérifier les autorisations spécifiques
  const loyaltyProgramAuths = user?.authorizations?.filter(auth => 
    auth.includes('loyalty') || auth.includes('qr') || auth.includes('point')
  );
  console.log('Autorisations liées au programme de fidélité:', loyaltyProgramAuths);
  
  // Vérifier l'URL de base
  console.log('URL de base du service:', reportingService.base_url);
  
  return {
    hasValidUser: !!user,
    hasValidToken: !!user?.accessToken,
    hasBackofficeRole: user?.roles?.includes('backoffice'),
    hasLoyaltyAuths: loyaltyProgramAuths?.length > 0
  };
}
