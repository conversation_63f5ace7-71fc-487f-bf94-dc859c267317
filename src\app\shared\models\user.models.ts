import { EmployeeType, UserCategory } from "../enums/user-category.enum";
import { Address, Point, Tonnage } from "../types";
import { Company } from "./company.models";
import { Store } from "./store.model";

export class User {
  _id?: string;
  firstName?: string;
  lastName?: string;
  matricuel?: string;
  email?: string;
  tel?: number;
  address?: Address;
  category?: UserCategory;
  roles?: string[];
  authorizations?: string[];
  profiles?: any[];
  password?: string;
  createAt?: number;
  updateAt?: number
  afrilandKey?: string;
  enable?: boolean;
  cni?: string;
  nui?: string;

  constructor(category: number) {
    this.email = '',
      this.firstName = '',
      this.lastName = '',
      this.password = '',
      this.cni = '',
      this.nui = '',
      this.address = { region: null, city: '', district: '' };
    this.category = category;
  }
}

export class Points {
  validate: number;
  unValidated: number;
  status: FidelityStatus;
  archived?: number;
}

export enum FidelityStatus {
  AMIGO = 1,
  COLOMBE = 2,
  PELICAN = 3,
}

export class Particular extends User {
  profession?: string;
  points?: Point
  defaultStore: Store;

  constructor(category: number) {
    super(category);
    this.profession = '';
  }
}
export class Retailer extends User {
  socialReason?: string;
  points?: Point;

  constructor(category: number) {
    super(category);
    this.socialReason = '';
  }
}
export class EmployeeCimencam extends User {
  isValidated?: boolean;
  isRetired?: boolean;
  direction?: string;
  service?: string;
  position?: string;
  employeeType: EmployeeType;
  matricule?: string;
  tonnage: Tonnage;
  associatedCompanies: Partial<Company[]>
  store?: Partial<Store>

  constructor(category: number) {
    super(category);
    this.direction = '';
    this.service = '';
    this.position = '';
    this.matricule = '';
  }
}
export class CompanyEmployee extends User {
  company?: Company;
  constructor(category: number) {
    super(category);
    this.company = new Company();
  }
}
export interface AuthUser extends User, EmployeeCimencam {
  accessToken: string;
}

export class Logo {
  _id?: string;
  company?: Partial<Company>
  user: Partial<User>;
  value: string;
  logoType: LogoType;
  created_at?: number;
  enable?: boolean;
}

export enum LogoType {
  LOGO = 1,
  SIGNATURE = 2,
}

export enum loyaltyProgramAction {
  CREATE = 'create_loyalty_program',
  UPDATE = 'update_loyalty_program',
  VIEW = 'view_Point_loyalty_program',
}