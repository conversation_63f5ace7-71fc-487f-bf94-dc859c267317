<div class=" account-list-page container page-container">
  <div class="account-list-page-container">
    <div class="header-list">
      <div class="title-container">
        <h2 class="title" i18n="@@listTitle">Liste des comptes Entreprise <span [ngStyle]="{'color': 'var(--clr-primary-400)',
                                'font-family': 'var(--montserrat-bold)'}">{{ title }}</span></h2>
        <div class="section-button">
          <button type="button" pButton i18n-label="@@ListReset" label="Réinitialiser" icon="pi pi-replay"
            class="p-button-text p-button-warning margin-rigth" (click)="reset()">
          </button>
          <button type="button" pButton i18n-label="@@ListFiltre" class="p-button-secondary margin-rigth" label="Filtre"
            icon="pi pi-filter" (click)="showSideBar = true">
          </button>
          <button pButton class="p-button-danger" i18n-label="@@ListCreateAccount" label=" Créer un compte"
            icon="pi pi-plus" *ngIf="this.commonService?.user?.authorizations.includes(userService?.userAction?.CREATE)"
            (click)="op.toggle($event)">
          </button>
          <button type="button" pButton class="p-button-primary" label="{{'EXPORT' | translate |async}}"
          icon="pi pi-file-pdf"  (click)="showDialogExport = true" >
        </button>
          <p-overlayPanel class="overlayPanel" #op [style]="{width: '195px'}" [dismissable]="true"
            [hideTransitionOptions]="'1ms'">
            <ng-template pTemplate>
              <div class="align-column">
                <div class="btn btn-icon btn-icon-edit" (click)="op.hide()" i18n="@@listcommerciaux"
                  *ngIf="this.commonService.user.authorizations.includes(userService.userAction?.CREATE)"
                  routerLink="form/COMMERCIAL">
                  Commerciaux
                </div>
                <div class="btn btn-icon btn-icon-edit" (click)="op.hide()" i18n="@@listtabset"
                  *ngIf="this.commonService.user.authorizations.includes(userService.userAction?.CREATE)"
                  routerLink="form/ADMINISTRATOR">
                  Administrateurs
                </div>
                <div class="btn btn-icon btn-icon-edit" (click)="op.hide()"
                  *ngIf="this.commonService.user.authorizations.includes(userService.userAction?.CREATE)"
                  routerLink="form/DonutAnimator">
                  Animateur Beignet
                </div>
              </div>
            </ng-template>
          </p-overlayPanel>
        </div>
      </div>
    </div>

    <div class="align-container">
      <div class="align-paginator">
        <p-paginator (onPageChange)="paginate($event)" [rows]="limit" [totalRecords]="total"
          [showJumpToPageDropdown]="true" [showPageLinks]="false">
        </p-paginator>
        <div class="title-h4">Total: {{total}}</div>
      </div>
      <p-tabView (onChange)="handleChangeTabUser()" [(activeIndex)]="userService.indexTab" class="bg-tertiary-100">
        <p-tabPanel i18n-header="@@listcommerciaux" header="Commerciaux">
          <div class="list-container">
            <mcw-progress-bar *ngIf="isLoading"></mcw-progress-bar>
            <p-table [value]="users" scrollHeight="67vh" [scrollable]="true" *ngIf="users?.length > 0 || isLoading">
              <ng-template pTemplate="header" class="customer">
                <tr>
                  <th style="min-width:50px; max-width:50px">N°</th>
                  <th style="min-width:170px; max-width:170px" i18n="@@listName2">Nom</th>
                  <th style="min-width:300px; max-width:300px">Email</th>
                  <th style="max-width:165px; min-width:165px" i18n="@@listMatricule2">Matricule</th>
                  <th style="max-width:165px; min-width:165px">Service</th>
                  <th style="min-width:170px; max-width:170px" i18n="@@listTel2">Téléphone</th>
                  <th class="iconsBtn"></th>
                </tr>
              </ng-template>
              <ng-template pTemplate="body" let-user let-i="rowIndex">
                <tr>
                  <td style="max-width:50px; min-width: 50px">{{i + offset + 1}}</td>
                  <td style="min-width:170px; max-width:170px" pTooltip="{{user.lastName}}" tooltipPosition="top">
                    {{user.lastName || 'N/A' | truncateString:15}}
                  </td>
                  <td style="min-width:300px; max-width:3000px" pTooltip="{{user.email}}" tooltipPosition="top">
                    {{user?.email | truncateString:30}}</td>
                  <td style="min-width:165px; max-width:165px">{{user?.matricule || 'N/A' | truncateString: 18}}</td>
                  <td style="min-width:165px; max-width:165px">{{user?.service || 'N/A' | truncateString:15}}</td>

                  <td style="min-width:170px; max-width:170px">{{user.tel || 'N/A ' | truncateString: 18}}</td>
                  <td class="action">
                    <button class="pi pi-ellipsis-v" (click)="op.toggle($event)">
                    </button>
                    <p-overlayPanel #op [style]="{width: '10 0px'}">
                      <ng-template pTemplate>
                        <div class="iconsBtn">
                          <div class="btn btn-icon btn-icon-view" i18n-pTooltip="@@listDetail" pTooltip="Détail"
                            tooltipPosition="top" [routerLink]="'form/' + userType + '/show/' + user?._id">
                            <i class="pi pi-book"></i>
                          </div>
                          <div class="btn btn-icon btn-icon-edit" i18n-pTooltip="@@listUpdate"
                            *ngIf="this.commonService.user.authorizations.includes(userService.userAction?.UPDATE) && user?.enable"
                            pTooltip="Modification" tooltipPosition="top"
                            [routerLink]="'form/' + userType + '/edit/'+ user?._id">
                            <i class="pi pi-user-edit"></i>
                          </div>
                          <div class="btn btn-icon btn-icon-view" i18n-pTooltip="@@listPWD"
                            *ngIf="this.commonService.user.authorizations.includes(userService.userAction?.CHANGE_PASSWORD) && user?.enable"
                            pTooltip="Mot de passe" tooltipPosition="top" (click)="showModalReset(user)">
                            <i class="pi pi-lock"></i>
                          </div>
                          <div (click)="deleteUser(user)"
                            *ngIf="this.commonService.user.authorizations.includes(userService.userAction?.DELETE)"
                            class="btn btn-icon" [ngClass]="user?.enable? 'btn-icon-delete' : 'btn-outline-success' "
                            [pTooltip]="user?.enable? ('Disable'|translate|async) : ('Enable'|translate|async) "
                            tooltipPosition="top">
                            <i [class]=" user?.enable? 'pi pi-eye-slash' : 'pi pi-eye' "></i>
                          </div>
                        </div>
                      </ng-template>
                    </p-overlayPanel>
                  </td>
                </tr>
              </ng-template>
            </p-table>
            <div *ngIf="users?.length <= 0 && !isLoading">
              <div class="not-found">
                <img src="assets/icons/data-not-fount.svg" alt="">
                <h6 i18n="@@listEmpty">
                  Aucun utilisateur trouvé
                </h6>
              </div>
            </div>
          </div>
        </p-tabPanel>

        <p-tabPanel i18n-header="@@listtabset" header="Administrateurs">
          <div class="list-container">
            <mcw-progress-bar *ngIf="isLoading"></mcw-progress-bar>
            <p-table [value]="users" [lazy]="true" [scrollable]="true" scrollHeight="67vh"
              *ngIf="users?.length > 0 || isLoading">
              <ng-template pTemplate="header" class="customer">
                <tr>
                  <th style="min-width:50px; max-width:50px">N°</th>
                  <th style="min-width:170px; max-width:170px" i18n="@@listName1">Nom</th>
                  <th style="min-width:170px; max-width:170px" i18n="@@listfirstName1">Prénom</th>
                  <th style="min-width:300px; max-width:300px">Email</th>
                  <th style="max-width:165px; min-width:165px" i18n="@@listMatricule1">Matricule</th>
                  <th style="min-width:170px; max-width:170px" i18n="@@listTel1">Téléphone</th>
                  <th class="iconsBtn"></th>
                </tr>
              </ng-template>
              <ng-template pTemplate="body" let-user let-i="rowIndex">
                <tr>
                  <td style="max-width:50px; max-width:50px">{{i + offset + 1}}</td>
                  <td style="min-width:170px; max-width:170px" pTooltip="{{user.firstName}}" tooltipPosition="top">
                    {{user.firstName || 'N/A' | truncateString:15}}
                  </td>
                  <td style="min-width:170px; max-width:170px" pTooltip="{{user.lastName}}" tooltipPosition="top">
                    {{user.lastName || 'N/A' |
                    truncateString:15}}</td>
                  <td style="min-width:300px; max-width:300px" pTooltip="{{user.email}}" tooltipPosition="top">
                    {{user.email | truncateString:30 }}</td>
                  <td style="min-width:165px; max-width:165px">{{user?.matricule || 'N/A'}}</td>
                  <!-- <td>{{user?.service || 'N/A' | truncateString:15}}</td> -->

                  <td style="min-width:170px; max-width:170px">{{user.tel || 'N/A' | truncateString:15}}</td>
                  <td class="action">
                    <button class="pi pi-ellipsis-v" (click)="op.toggle($event)">
                    </button>
                    <p-overlayPanel #op [style]="{width: '10 0px'}">
                      <ng-template pTemplate>
                        <div class="iconsBtn">
                          <div class="btn btn-icon btn-icon-view" i18n-ptooltip="@@listDetail" pTooltip="Détails"
                            tooltipPosition="top" [routerLink]="'form/' + userType + '/show/' + user?._id">
                            <i class="pi pi-book"></i>
                          </div>
                          <div class="btn btn-icon btn-icon-edit"
                            *ngIf="this.commonService.user.authorizations.includes(userService.userAction?.UPDATE) && user?.enable"
                            pTooltip="Modification" i18n-ptooltip="@@listUpdate" tooltipPosition="top"
                            [routerLink]="'form/' + userType + '/edit/'+ user?._id">
                            <i class="pi pi-user-edit"></i>
                          </div>
                          <div class="btn btn-icon btn-icon-view" i18n-ptooltip="@@listPWD"
                            *ngIf="this.commonService.user.authorizations.includes(userService.userAction?.UPDATE) && user?.enable"
                            pTooltip="Mot de passe" tooltipPosition="top" (click)="showModalReset(user)">
                            <i class="pi pi-lock"></i>
                          </div>
                          <div *ngIf="this.commonService.user.authorizations.includes(userService.userAction?.DELETE)"
                            (click)="deleteUser(user)" class="btn btn-icon"
                            [ngClass]="user?.enable? 'btn-icon-delete' : 'btn-outline-success' "
                            [pTooltip]="user?.enable? ('Disable'| translate | async) : ('Enable'| translate | async)"
                            tooltipPosition="top">
                            <i [class]=" user?.enable? 'pi pi-eye-slash' : 'pi pi-eye' "></i>
                          </div>
                        </div>
                      </ng-template>
                    </p-overlayPanel>
                  </td>
                </tr>
              </ng-template>
            </p-table>
            <div *ngIf="users?.length <= 0">
              <div class="not-found">
                <img src="assets/icons/data-not-fount.svg" alt="">
                <h6 i18n="@@listEmpty">
                  Aucun utilisateur trouvé
                </h6>
              </div>
            </div>
          </div>
        </p-tabPanel>
        
        <p-tabPanel header="Animateurs Beignets">
          <div class="list-container">
            <mcw-progress-bar *ngIf="isLoading"></mcw-progress-bar>
            <p-table [value]="users" [lazy]="true" [scrollable]="true" scrollHeight="67vh"
              *ngIf="users?.length > 0 || isLoading">
              <ng-template pTemplate="header" class="customer">
                <tr>
                  <th style="min-width:50px; max-width:50px">N°</th>
                  <th style="min-width:170px; max-width:170px">Nom</th>
                  <th style="min-width:170px; max-width:170px">Prénom</th>
                  <th style="min-width:300px; max-width:300px">Email</th>
                  <th style="max-width:165px; min-width:165px">Matricule</th>
                  <th style="min-width:170px; max-width:170px">Téléphone</th>
                  <th class="iconsBtn"></th>
                </tr>
              </ng-template>
              <ng-template pTemplate="body" let-user let-i="rowIndex">
                <tr>
                  <td style="max-width:50px; max-width:50px">{{i + offset + 1}}</td>
                  <td style="min-width:170px; max-width:170px" pTooltip="{{user.firstName}}" tooltipPosition="top">
                    {{user.firstName || 'N/A' | truncateString:15}}
                  </td>
                  <td style="min-width:170px; max-width:170px" pTooltip="{{user.lastName}}" tooltipPosition="top">
                    {{user.lastName || 'N/A' | truncateString:15}}
                  </td>
                  <td style="min-width:300px; max-width:300px" pTooltip="{{user.email}}" tooltipPosition="top">
                    {{user.email | truncateString:30 }}</td>
                  <td style="min-width:165px; max-width:165px">{{user?.matricule || 'N/A'}}</td>
                  <td style="min-width:170px; max-width:170px">{{user.tel || 'N/A' | truncateString:15}}</td>
                  <td class="action">
                    <button class="pi pi-ellipsis-v" (click)="op.toggle($event)"></button>
                    <p-overlayPanel #op [style]="{width: '10 0px'}">
                      <ng-template pTemplate>
                        <div class="iconsBtn">
                          <div class="btn btn-icon btn-icon-view" pTooltip="Détails"
                            tooltipPosition="top" [routerLink]="'form/ANIMATEUR/show/' + user?._id">
                            <i class="pi pi-book"></i>
                          </div>
                          <div class="btn btn-icon btn-icon-edit"
                            *ngIf="this.commonService.user.authorizations.includes(userService.userAction?.UPDATE) && user?.enable"
                            pTooltip="Modification" tooltipPosition="top"
                            [routerLink]="'form/ANIMATEUR/edit/'+ user?._id">
                            <i class="pi pi-user-edit"></i>
                          </div>
                          <div class="btn btn-icon btn-icon-view"
                            *ngIf="this.commonService.user.authorizations.includes(userService.userAction?.UPDATE) && user?.enable"
                            pTooltip="Mot de passe" tooltipPosition="top" (click)="showModalReset(user)">
                            <i class="pi pi-lock"></i>
                          </div>
                          <div *ngIf="this.commonService.user.authorizations.includes(userService.userAction?.DELETE)"
                            (click)="deleteUser(user)" class="btn btn-icon"
                            [ngClass]="user?.enable? 'btn-icon-delete' : 'btn-outline-success'"
                            [pTooltip]="user?.enable? ('Disable'| translate | async) : ('Enable'| translate | async)"
                            tooltipPosition="top">
                            <i [class]="user?.enable? 'pi pi-eye-slash' : 'pi pi-eye'"></i>
                          </div>
                        </div>
                      </ng-template>
                    </p-overlayPanel>
                  </td>
                </tr>
              </ng-template>
            </p-table>
            <div *ngIf="users?.length <= 0 && !isLoading">
              <div class="not-found">
                <img src="assets/icons/data-not-fount.svg" alt="">
                <h6>Aucun animateur trouvé</h6>
              </div>
            </div>
          </div>
        </p-tabPanel>
      </p-tabView>
    </div>
  </div>
</div>

<p-sidebar [(visible)]="showSideBar" [style]="{width:'22em'}" position="right">
  <section class="filter-sidebar">
    <section class="header-filter">
      <span></span>
      <button type="button" pButton i18n-label="@@listreset" label="Réinitialiser" icon="pi pi-replay"
        class="p-button-text p-button-warning" (click)="reset()"></button>
    </section>
    <section class="body-filter">
      <form class="form">
        <div class="input-group">
          <label for="status" i18n="@@liststatus">Statut </label>
          <p-dropdown name="type" [options]="statusAccount" optionValue="code" optionLabel="name"
            [(ngModel)]="userService.filterListUser.enable" i18n-placeholder="@@listselectStatus"
            placeholder="{{'Sélectionner un statut'}}">
          </p-dropdown>
        </div>
        <div class="input-group">
          <label for="email">Email </label>
          <p-autoComplete type="email" [(ngModel)]="userService.filterListUser.email" [suggestions]="filteredUsers"
            placeholder="Email utilisateur" i18n-placeholder="@@listselectEmail"
            (completeMethod)="searchUsers($event)"></p-autoComplete>
          <!-- <input name="email" type="email" [(ngModel)]="userService.filterListUser.email" pInputText
            i18n-placeholder="@@listselectEmail" placeholder="Email utilisateur" /> -->
        </div>
        <div class="input-group">
          <label for="tel">Tel </label>
          <input name="tel" type="number" [(ngModel)]="userService.filterListUser.tel" pInputText
            placeholder="{{userService?.filterListUser?.tel}}" />
        </div>
      </form>
    </section>
    <section class="footer-filter ">
      <button pButton pRipple type="button" i18n-label="@@listOut1" label="Annuler" icon="pi pi-times"
        class="p-button-outlined p-button-secondary" (click)=" showSideBar = false">
      </button>
      <button pButton pRipple type="button" i18n-label="@@listFilter1" label="Filtrer" icon="pi pi-search"
        [loading]="isLoading" class="p-button-success" (click)="getUsers(); showSideBar = false">
      </button>
    </section>
  </section>
</p-sidebar>

<p-confirmDialog i18n-acceptLabel="@@listDisabled"
  acceptLabel="{{!userService.filterListUser?.enable? ('enable' | translate |async) : ('disable' | translate |async)}}"
  [style]="{width: '30vw'}" i18n-rejectLabel="@@listReject6" rejectLabel="Annuler"
  [acceptIcon]="isLoadingInModal? 'pi pi-spin pi-spinner' : 'pi pi-check'"
  rejectButtonStyleClass="p-button-text bg-tertiary-400 clr-default-400"
  [acceptButtonStyleClass]="userService.filterListUser?.enable? 'bg-secondary-400 border-secondary-400' : '' "
  defaultFocus="none">
</p-confirmDialog>
<p-toast></p-toast>

<p-dialog i18n-acceptLabel="@@listConfirm-delete" acceptButtonStyle i18n-header="@@listheadear-container"
  header="{{('PASSWORD' |translate | async ) + user?.lastName || user?.email }}" [(visible)]="modalReset" [modal]="true"
  [style]="{width: '30vw'}" [draggable]="false" [resizable]="false">
  <div class="input-group-float">
    <label i18n="@@newPwd3">Nouveau mot de passe </label>
    <p-password [toggleMask]="true" [(ngModel)]="user.password"></p-password>
  </div>
  <div class="input-group-float">
    <label i18n="@@listconfirmNewPwd3">Confirmation mot de passe</label>
    <p-password [toggleMask]="true" [(ngModel)]="confirmPassword"></p-password>
  </div>

  <ng-template pTemplate="footer">

    <p-button icon="pi pi-times" (click)="modalReset=false" i18n-label="@@listOut3" label="Annuler"
      styleClass="bg-tertiary-400 border-tertiary-400">
    </p-button>
    <p-button [icon]="isLoadingInModal? 'pi pi-spin pi-spinner' : 'pi pi-check'" (click)="resetpassword()"
      i18n-label="@@listReset3" label="Réinitialiser" styleClass="bg-primary-400 border-primary-400"></p-button>

  </ng-template>
</p-dialog>

<p-dialog header="EXPORTE LA LISTE " [(visible)]="showDialogExport" [modal]="true"
  [style]="{width: '30%'}" [draggable]="true" [resizable]="true">
  
    <section class="body-filter">
      <form class="form">
        <div class="input-group">
          <label for="status" i18n="@@liststatus">Statut </label>
          <p-dropdown name="type" [options]="statusAccount" optionValue="code" optionLabel="name"
            [(ngModel)]="userService.filterListUser.enable" i18n-placeholder="@@listselectStatus"
            placeholder="{{'Sélectionner un statut'}}">
          </p-dropdown>
        </div>
        <div class="input-group">
          <label for="email">Email </label>
          <p-autoComplete type="email" [(ngModel)]="userService.filterListUser.email" [suggestions]="filteredUsers"
            placeholder="Email utilisateur" i18n-placeholder="@@listselectEmail"
            (completeMethod)="searchUsers($event)"></p-autoComplete>
          <!-- <input name="email" type="email" [(ngModel)]="userService.filterListUser.email" pInputText
            i18n-placeholder="@@listselectEmail" placeholder="Email utilisateur" /> -->
        </div>
        <div class="input-group">
          <label for="tel">Tel </label>
          <input name="tel" type="number" [(ngModel)]="userService.filterListUser.tel" pInputText
            placeholder="{{userService?.filterListUser?.tel}}" />
        </div>
        <div class="input-group">
          <label for="loadNumber">Totale d' élements a exporte, Maximun: {{total}}</label>
          <p-inputNumber [(ngModel)]="limit" [size]="100"></p-inputNumber>
      
      </div>

      </form>


    <section class="footer-export">
      <button pButton pRipple type="button" label="{{'close' | translate | async}}" icon="pi pi-times"
        class="p-button-outlined p-button-secondary" (click)="showDialogExport = false">
      </button>
      <button pButton pRipple type="button" label="{{'EXPORT' | translate |async}}" icon="pi pi-search"
        [loading]="isLoading" class="p-button-success" (click)=" exportToExcel() ; showDialogExport = false ">
      </button>
    </section>
  </section>


</p-dialog>